"""
Test script to verify StateManager upgrade mechanism for clear_all_script_history method.

This test verifies that the fix for the AttributeError in stage9.py works correctly
by testing the StateManager upgrade mechanism.
"""

import sys
import os
import tempfile
import unittest
from unittest.mock import Mock, patch

# Add the current directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from state_manager import StateManager, StateStage


class TestStateManagerUpgrade(unittest.TestCase):
    """Test StateManager upgrade mechanism for script history methods."""

    def setUp(self):
        """Set up test environment."""
        self.mock_st = Mock()
        self.mock_st.session_state = {}

    def test_new_state_manager_has_clear_all_script_history(self):
        """Test that a new StateManager instance has the clear_all_script_history method."""
        state = StateManager()
        
        # Verify the method exists
        self.assertTrue(hasattr(state, 'clear_all_script_history'))
        self.assertTrue(callable(getattr(state, 'clear_all_script_history')))
        
        # Verify other script history methods exist
        self.assertTrue(hasattr(state, 'get_all_scripts_with_history'))
        self.assertTrue(hasattr(state, 'add_script_to_history'))

    def test_state_manager_init_in_session_new_instance(self):
        """Test StateManager initialization in session state (new instance)."""
        state = StateManager()
        
        # Initialize in session
        state.init_in_session(self.mock_st)
        
        # Verify state is stored in session
        self.assertIn("state", self.mock_st.session_state)
        stored_state = self.mock_st.session_state["state"]
        
        # Verify the stored state has all required methods
        self.assertTrue(hasattr(stored_state, 'clear_all_script_history'))
        self.assertTrue(hasattr(stored_state, 'get_all_scripts_with_history'))
        self.assertTrue(hasattr(stored_state, 'add_script_to_history'))

    def test_state_manager_upgrade_existing_instance(self):
        """Test StateManager upgrade of existing instance missing methods."""
        # Create a mock existing state that's missing the new methods
        existing_state = Mock()
        existing_state.current_stage = StateStage.STAGE1_UPLOAD
        existing_state._script_storage = None
        existing_state.script_history = []
        existing_state.script_metadata = {}
        
        # Simulate missing methods (this is the bug scenario)
        existing_state.set_execution_error = Mock()  # This exists
        # clear_all_script_history is missing (this causes the AttributeError)
        
        # Store in session state
        self.mock_st.session_state["state"] = existing_state
        
        # Create new StateManager and initialize
        state = StateManager()
        state.init_in_session(self.mock_st)
        
        # Verify the existing state was upgraded
        upgraded_state = self.mock_st.session_state["state"]
        
        # The upgraded state should now have the missing methods
        self.assertTrue(hasattr(upgraded_state, 'clear_all_script_history'))
        self.assertTrue(hasattr(upgraded_state, 'get_all_scripts_with_history'))
        self.assertTrue(hasattr(upgraded_state, 'add_script_to_history'))

    def test_clear_all_script_history_method_functionality(self):
        """Test that the clear_all_script_history method works correctly."""
        state = StateManager()
        
        # Add some mock script history
        state.script_history = [
            {"id": "script1", "content": "test script 1"},
            {"id": "script2", "content": "test script 2"}
        ]
        state.script_metadata = {
            "script1": {"type": "test"},
            "script2": {"type": "test"}
        }
        
        # Test without confirmation (should fail)
        result = state.clear_all_script_history(confirm=False, reason="Test without confirmation")
        self.assertFalse(result)
        
        # Verify data is still there
        self.assertEqual(len(state.script_history), 2)
        self.assertEqual(len(state.script_metadata), 2)
        
        # Test with confirmation (should succeed)
        result = state.clear_all_script_history(confirm=True, reason="Test with confirmation")
        self.assertTrue(result)
        
        # Verify data is cleared
        self.assertEqual(len(state.script_history), 0)
        self.assertEqual(len(state.script_metadata), 0)

    def test_defensive_programming_in_stage9(self):
        """Test that stage9 defensive programming works with missing methods."""
        # Import stage9 function
        from stages.stage9 import stage9_browse_scripts
        
        # Create a mock state without the method (simulating the bug)
        mock_state = Mock()
        mock_state.script_history = []
        mock_state.script_metadata = {}
        
        # Remove the method to simulate the AttributeError scenario
        if hasattr(mock_state, 'clear_all_script_history'):
            delattr(mock_state, 'clear_all_script_history')
        
        # Mock Streamlit functions
        with patch('stages.stage9.st') as mock_st:
            mock_st.markdown = Mock()
            mock_st.info = Mock()
            mock_st.expander = Mock()
            mock_st.columns = Mock(return_value=[Mock(), Mock()])
            mock_st.button = Mock(return_value=False)
            mock_st.checkbox = Mock(return_value=False)
            mock_st.selectbox = Mock(return_value='All')
            mock_st.text_input = Mock(return_value='')
            mock_st.radio = Mock(return_value='side_by_side')
            mock_st.warning = Mock()
            mock_st.session_state = {}
            
            # This should not raise an AttributeError due to defensive programming
            try:
                stage9_browse_scripts(mock_state)
                test_passed = True
            except AttributeError as e:
                if 'clear_all_script_history' in str(e):
                    test_passed = False
                else:
                    # Some other AttributeError, re-raise
                    raise
            except Exception:
                # Other exceptions are fine, we're just testing for AttributeError
                test_passed = True
            
            self.assertTrue(test_passed, "stage9_browse_scripts should handle missing clear_all_script_history method gracefully")


if __name__ == '__main__':
    print("Testing StateManager upgrade mechanism for clear_all_script_history method...")
    print("=" * 80)
    
    # Run the tests
    unittest.main(verbosity=2)
