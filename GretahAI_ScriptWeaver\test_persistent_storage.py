#!/usr/bin/env python3
"""
Test script for persistent storage functionality.

This script tests the new persistent storage system for Stage 9 Script Browser.
"""

import os
import sys
import tempfile
from datetime import datetime

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_script_storage():
    """Test the ScriptStorage class functionality."""
    print("🧪 Testing ScriptStorage functionality...")

    try:
        from core.script_storage import ScriptStorage

        # Create a temporary database for testing
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            test_db_path = tmp_file.name

        print(f"📁 Using test database: {test_db_path}")

        # Initialize storage
        storage = ScriptStorage(test_db_path)
        print("✅ ScriptStorage initialized successfully")

        # Test saving scripts
        script_content = """
import pytest
from selenium import webdriver

def test_login():
    driver = webdriver.Chrome()
    driver.get("https://example.com")
    # Test login functionality
    driver.quit()
"""

        script_id = storage.save_script(
            script_content=script_content,
            script_type='step',
            test_case_id='TC001',
            step_no='1',
            file_path='/test/script.py',
            metadata={
                'test_description': 'Login test',
                'validation_status': 'pending'
            }
        )
        print(f"✅ Script saved with ID: {script_id}")

        # Test retrieving scripts
        all_scripts = storage.get_all_scripts()
        print(f"✅ Retrieved {len(all_scripts)} scripts")

        if all_scripts:
            script = all_scripts[0]
            print(f"   - Script type: {script['type']}")
            print(f"   - Test case: {script['test_case_id']}")
            print(f"   - Content length: {len(script['content'])} chars")

        # Test statistics
        stats = storage.get_script_statistics()
        print(f"✅ Statistics: {stats['total_scripts']} total scripts")

        # Test cleanup
        storage.close()
        print("✅ Storage closed successfully")

        # Clean up test database
        os.unlink(test_db_path)
        print("🧹 Test database cleaned up")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_state_manager_integration():
    """Test StateManager integration with persistent storage."""
    print("\n🧪 Testing StateManager integration...")

    try:
        from state_manager import StateManager
        import streamlit as st

        # Mock Streamlit session state
        class MockSessionState:
            def __init__(self):
                self.data = {}

            def get(self, key, default=None):
                return self.data.get(key, default)

            def __setitem__(self, key, value):
                self.data[key] = value

            def __getitem__(self, key):
                return self.data[key]

            def __contains__(self, key):
                return key in self.data

        # Mock streamlit module
        class MockStreamlit:
            def __init__(self):
                self.session_state = MockSessionState()

        mock_st = MockStreamlit()

        # Initialize StateManager
        state = StateManager()
        state.init_in_session(mock_st)
        print("✅ StateManager initialized with persistent storage")

        # Test adding script to history
        state.add_script_to_history(
            script_content="# Test script content",
            script_type='test',
            step_no='1',
            metadata={'test': True}
        )
        print("✅ Script added to history")

        # Test adding optimized script with enhanced metadata
        optimized_metadata = {
            'optimization_complete': True,
            'test_case_id': 'TC001',
            'optimization_timestamp': '2025-01-01T12:00:00',
            'optimization_duration': 45.2,
            'script_source': 'combined_script',
            'validation_results': {
                'quality_score': 85,
                'syntax_valid': True,
                'ready_for_execution': True,
                'issues_found': []
            },
            'original_script_path': '/path/to/original.py',
            'optimization_status': 'optimized'
        }

        state.add_script_to_history(
            script_content="""
import pytest
from selenium import webdriver

@pytest.fixture
def driver():
    driver = webdriver.Chrome()
    yield driver
    driver.quit()

def test_optimized_login(driver):
    driver.get("https://example.com")
    # Optimized test logic here
    assert "Example" in driver.title
""",
            script_type='optimized',
            step_no=None,
            metadata=optimized_metadata
        )
        print("✅ Optimized script added to history")

        # Test getting scripts with history
        all_scripts = state.get_all_scripts_with_history()
        print(f"✅ Retrieved {len(all_scripts)} scripts with history")

        # Verify optimized script is properly stored
        optimized_scripts = [s for s in all_scripts if s.get('type') == 'optimized']
        if optimized_scripts:
            opt_script = optimized_scripts[0]
            print(f"   - Optimized script found with optimization_status: {opt_script.get('optimization_status')}")
            print(f"   - Quality score: {opt_script.get('metadata', {}).get('validation_results', {}).get('quality_score', 'N/A')}")

        # Test statistics
        stats = state.get_script_statistics()
        print(f"✅ Statistics retrieved: {stats.get('total_scripts', 0)} scripts")
        print(f"   - Script types: {stats.get('script_types', {})}")

        return True

    except Exception as e:
        print(f"❌ StateManager integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Starting persistent storage tests...\n")

    # Test 1: Basic storage functionality
    test1_passed = test_script_storage()

    # Test 2: StateManager integration
    test2_passed = test_state_manager_integration()

    # Summary
    print("\n📊 Test Results:")
    print(f"   ScriptStorage: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   StateManager Integration: {'✅ PASSED' if test2_passed else '❌ FAILED'}")

    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! Persistent storage is working correctly.")
        return 0
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
